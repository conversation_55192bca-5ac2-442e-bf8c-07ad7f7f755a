-- Enhance email_messages table with Message-ID tracking for email thread correlation
-- This migration adds the critical Message-ID header field to enable tracking between
-- outgoing RFQ emails and incoming Gmail responses

-- Add Message-ID column to email_messages table for email tracking
ALTER TABLE public.email_messages
ADD COLUMN message_id TEXT;

-- Add comment for the new column
COMMENT ON COLUMN public.email_messages.message_id IS 'Message-ID header value from Gmail API for email tracking and thread correlation with outgoing RFQ emails';

-- Create index for efficient Message-ID lookups (critical for tracking outgoing emails)
CREATE INDEX IF NOT EXISTS idx_email_messages_message_id ON public.email_messages (message_id);

-- Index for In-Reply-To lookups (existing column, adding index for performance)
CREATE INDEX IF NOT EXISTS idx_email_messages_in_reply_to_lookup ON public.email_messages (in_reply_to);

-- Fix Supabase linter suggestions: Add indexes for unindexed foreign keys

-- Email settings table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_email_settings_created_by ON public.email_settings (created_by);
CREATE INDEX IF NOT EXISTS idx_email_settings_updated_by ON public.email_settings (updated_by);

-- Provider contacts table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_provider_contacts_created_by ON public.provider_contacts (created_by);
CREATE INDEX IF NOT EXISTS idx_provider_contacts_updated_by ON public.provider_contacts (updated_by);

-- Provider routes table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_provider_routes_created_by ON public.provider_routes (created_by);
CREATE INDEX IF NOT EXISTS idx_provider_routes_updated_by ON public.provider_routes (updated_by);

-- Providers table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_providers_created_by ON public.providers (created_by);
CREATE INDEX IF NOT EXISTS idx_providers_updated_by ON public.providers (updated_by);

-- RFQ bids table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfq_bids_original_email_id ON public.rfq_bids (original_email_id);

-- RFQ incoming emails table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfq_incoming_emails_extracted_bid_id ON public.rfq_incoming_emails (extracted_bid_id);

-- RFQs table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_rfqs_cargo_type_id ON public.rfqs (cargo_type_id);
CREATE INDEX IF NOT EXISTS idx_rfqs_equipment_type_id ON public.rfqs (equipment_type_id);

-- User roles table foreign key indexes
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON public.user_roles (role_id);

-- Remove unused indexes that were flagged by Supabase linter
-- These indexes consume storage and maintenance overhead without providing query benefits

-- RFQs table unused indexes
DROP INDEX IF EXISTS idx_rfqs_created_by;
DROP INDEX IF EXISTS idx_rfqs_status;
DROP INDEX IF EXISTS idx_rfqs_sequence_number;
DROP INDEX IF EXISTS idx_rfqs_origin_country_id;
DROP INDEX IF EXISTS idx_rfqs_destination_country_id;
DROP INDEX IF EXISTS idx_rfqs_origin_postal_code;
DROP INDEX IF EXISTS idx_rfqs_destination_postal_code;

-- RFQ incoming emails table unused indexes
DROP INDEX IF EXISTS idx_rfq_incoming_emails_in_reply_to;
DROP INDEX IF EXISTS idx_rfq_incoming_emails_is_processed;
DROP INDEX IF EXISTS idx_rfq_incoming_emails_thread_id;

-- RFQ bids table unused indexes
DROP INDEX IF EXISTS idx_rfq_bids_status;

-- RFQ outgoing emails table unused indexes
DROP INDEX IF EXISTS idx_rfq_outgoing_emails_message_id;
DROP INDEX IF EXISTS idx_rfq_outgoing_emails_reply_to;
DROP INDEX IF EXISTS idx_rfq_outgoing_emails_thread_id;
DROP INDEX IF EXISTS idx_rfq_outgoing_emails_tracking_id;

-- Countries table unused indexes
DROP INDEX IF EXISTS idx_countries_continent;
DROP INDEX IF EXISTS idx_countries_region;

-- Equipment types table unused indexes
DROP INDEX IF EXISTS idx_equipment_types_category;

-- Provider contacts table unused indexes
DROP INDEX IF EXISTS idx_provider_contacts_email;

-- RFQ cargo types table unused indexes
DROP INDEX IF EXISTS idx_rfq_cargo_types_cargo_type_id;

-- Watched email accounts table unused indexes
DROP INDEX IF EXISTS idx_watched_email_accounts_email;
DROP INDEX IF EXISTS idx_watched_email_accounts_status;
DROP INDEX IF EXISTS idx_watched_email_accounts_created_by;
DROP INDEX IF EXISTS idx_watched_email_accounts_last_sync_at;

-- Email messages table unused indexes
DROP INDEX IF EXISTS idx_email_messages_gmail_id;
DROP INDEX IF EXISTS idx_email_messages_gmail_thread_id;
DROP INDEX IF EXISTS idx_email_messages_category;
DROP INDEX IF EXISTS idx_email_messages_in_reply_to;

-- Email attachments table unused indexes
DROP INDEX IF EXISTS idx_email_attachments_gmail_attachment_id;
DROP INDEX IF EXISTS idx_email_attachments_filename;
DROP INDEX IF EXISTS idx_email_attachments_created_at;