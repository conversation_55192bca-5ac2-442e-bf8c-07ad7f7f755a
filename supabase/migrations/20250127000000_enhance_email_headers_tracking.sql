-- Enhance email_messages table with comprehensive header tracking
-- This migration adds critical email headers for thread tracking and outgoing email correlation

-- Add new columns to email_messages table for enhanced header tracking
ALTER TABLE public.email_messages 
ADD COLUMN message_id TEXT,
ADD COLUMN references TEXT,
ADD COLUMN raw_headers J<PERSON>N<PERSON>,
ADD COLUMN received_headers TEXT[],
ADD COLUMN authentication_results TEXT,
ADD COLUMN return_path TEXT,
ADD COLUMN x_originating_ip TEXT,
ADD COLUMN x_mailer TEXT,
ADD COLUMN auto_submitted TEXT,
ADD COLUMN precedence TEXT;

-- Add comments for the new columns
COMMENT ON COLUMN public.email_messages.message_id IS 'Message-ID header value for email tracking and thread correlation';
COMMENT ON COLUMN public.email_messages.references IS 'References header containing Message-IDs of all messages in the thread';
COMMENT ON COLUMN public.email_messages.raw_headers IS 'JSONB storage of all email headers for future flexibility and analysis';
COMMENT ON COLUMN public.email_messages.received_headers IS 'Array of Received headers showing email routing path';
COMMENT ON COLUMN public.email_messages.authentication_results IS 'Authentication-Results header for SPF, DKIM, DMARC validation';
COMMENT ON COLUMN public.email_messages.return_path IS 'Return-Path header for bounce handling';
COMMENT ON COLUMN public.email_messages.x_originating_ip IS 'X-Originating-IP header showing source IP address';
COMMENT ON COLUMN public.email_messages.x_mailer IS 'X-Mailer header identifying the email client used';
COMMENT ON COLUMN public.email_messages.auto_submitted IS 'Auto-Submitted header for automated email detection';
COMMENT ON COLUMN public.email_messages.precedence IS 'Precedence header indicating email priority/type';

-- Create indexes for efficient lookups
-- Critical index for Message-ID lookups (primary use case for tracking outgoing emails)
CREATE INDEX IF NOT EXISTS idx_email_messages_message_id ON public.email_messages (message_id);

-- Index for References header searches (thread reconstruction)
CREATE INDEX IF NOT EXISTS idx_email_messages_references ON public.email_messages USING GIN (to_tsvector('english', references));

-- Index for raw_headers JSONB queries
CREATE INDEX IF NOT EXISTS idx_email_messages_raw_headers ON public.email_messages USING GIN (raw_headers);

-- Index for In-Reply-To lookups (existing column, adding index for performance)
CREATE INDEX IF NOT EXISTS idx_email_messages_in_reply_to_lookup ON public.email_messages (in_reply_to);

-- Enhance rfq_outgoing_emails table to store generated Message-IDs
ALTER TABLE public.rfq_outgoing_emails 
ADD COLUMN generated_message_id TEXT;

-- Add comment for the new column
COMMENT ON COLUMN public.rfq_outgoing_emails.generated_message_id IS 'Generated Message-ID header value for tracking correlation with incoming responses';

-- Create index for Message-ID lookups in outgoing emails
CREATE INDEX IF NOT EXISTS idx_rfq_outgoing_emails_message_id ON public.rfq_outgoing_emails (generated_message_id);

-- Create a function to extract Message-ID from various header formats
-- This handles both <message-id@domain> and message-id@domain formats
CREATE OR REPLACE FUNCTION extract_clean_message_id(message_id_header TEXT)
RETURNS TEXT AS $$
BEGIN
    IF message_id_header IS NULL OR message_id_header = '' THEN
        RETURN NULL;
    END IF;
    
    -- Remove angle brackets if present
    RETURN TRIM(BOTH '<>' FROM TRIM(message_id_header));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create a function to find related emails by Message-ID
-- This function helps correlate outgoing and incoming emails
CREATE OR REPLACE FUNCTION find_related_emails_by_message_id(target_message_id TEXT)
RETURNS TABLE (
    email_id UUID,
    email_type TEXT,
    message_id TEXT,
    in_reply_to TEXT,
    references TEXT,
    subject TEXT,
    date_received TIMESTAMPTZ
) AS $$
BEGIN
    -- Clean the target Message-ID
    target_message_id := extract_clean_message_id(target_message_id);
    
    IF target_message_id IS NULL THEN
        RETURN;
    END IF;
    
    -- Find emails that reference this Message-ID
    RETURN QUERY
    SELECT 
        em.id as email_id,
        'incoming'::TEXT as email_type,
        extract_clean_message_id(em.message_id) as message_id,
        extract_clean_message_id(em.in_reply_to) as in_reply_to,
        em.references,
        em.subject,
        em.date_received
    FROM public.email_messages em
    WHERE 
        extract_clean_message_id(em.in_reply_to) = target_message_id
        OR extract_clean_message_id(em.message_id) = target_message_id
        OR em.references ILIKE '%' || target_message_id || '%'
    
    UNION ALL
    
    -- Find outgoing emails with this Message-ID
    SELECT 
        roe.id as email_id,
        'outgoing'::TEXT as email_type,
        extract_clean_message_id(roe.generated_message_id) as message_id,
        NULL::TEXT as in_reply_to,
        NULL::TEXT as references,
        roe.subject,
        roe.sent_at as date_received
    FROM public.rfq_outgoing_emails roe
    WHERE extract_clean_message_id(roe.generated_message_id) = target_message_id
    
    ORDER BY date_received DESC;
END;
$$ LANGUAGE plpgsql;

-- Add a trigger to automatically update the updated_at timestamp when headers are modified
CREATE OR REPLACE FUNCTION update_email_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for email_messages table (if not already exists)
DROP TRIGGER IF EXISTS trigger_update_email_messages_updated_at ON public.email_messages;
CREATE TRIGGER trigger_update_email_messages_updated_at
    BEFORE UPDATE ON public.email_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_email_messages_updated_at();
