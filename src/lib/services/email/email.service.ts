/**
 * Email Service - Server-Side Implementation
 *
 * This is the canonical implementation of the email service for server-side use.
 * It provides functionality for sending emails via SendGrid, including:
 * - Regular emails (sendEmail)
 * - Bulk emails (sendBulkEmails)
 * - RFQ-specific emails with templates (sendRFQEmail)
 *
 * Features:
 * - Email validation
 * - Sandbox mode for testing
 * - Plain text alternative for better email client compatibility
 * - Environment-based sandbox mode control (NODE_ENV=development forces sandbox)
 * - BCC support
 * - Attachment handling
 * - Message-ID generation
 * - Reply-To address customization
 * - Performance optimization: Functions can share email settings to reduce database calls
 *
 * IMPORTANT: This file should only be imported on the server side.
 * For client-side components, use '@/lib/services/email/email-client.service.ts' instead.
 *
 * Usage:
 * ```typescript
 * import { sendEmail, sendRFQEmail, sendBulkEmails } from '@/lib/services/email/email.service';
 * ```
 *
 * Configuration:
 * - Requires SendGrid API key in environment variables
 * - Email settings are stored in the database (email_settings table)
 * - Supports sandbox mode for testing without sending real emails
 * - Environment variables:
 *   - SENDGRID_API_KEY: SendGrid API key
 *   - EMAIL_FORCE_SANDBOX: When set to 'true', forces sandbox mode regardless of database settings
 *   - NODE_ENV: When set to 'development', forces sandbox mode unless EMAIL_FORCE_SANDBOX is 'false'
 */

// This file should only be imported on the server side
// Add a check to prevent client-side usage
if (typeof window !== "undefined") {
  throw new Error(
    "email.service.ts should only be imported on the server side. " +
      "For client components, use email-client.service.ts instead.",
  );
}

import sgMail from "@sendgrid/mail";
import { createLogger } from "@/lib/utils/logger/logger";
import { rfqTemplate } from "@/lib/services/email/templates/rfq";
import {
  EmailData,
  EmailRecipient,
  RFQEmailData,
  validateEmailData,
  validateRFQEmailData,
  formatEmailError,
} from "./email-core.service";
import {
  generateMessageId,
  generateRfqPlusAddress,
  extractDomainFromEmail,
} from "@/lib/utils/email-utils";
import {
  getFormattedEmailSettings
} from "@/lib/services/email-settings.service";

// Create a logger instance for this module
const logger = createLogger("EmailService");

// Note: Email status tracking via webhooks is not implemented
// In a production environment, you would set up SendGrid webhooks to update email status
// For now, we'll update the status directly in the database after sending

// Initialize SendGrid with API key
const sendgridApiKey = process.env.SENDGRID_API_KEY || "";

// Validate SendGrid API key
if (!sendgridApiKey) {
  logger.error("SENDGRID_API_KEY is not defined in environment variables");
} else if (sendgridApiKey.length < 20) {
  // Most API keys are longer than 20 characters
  logger.error("SENDGRID_API_KEY appears to be invalid (too short)");
}

// Set the API key for SendGrid
sgMail.setApiKey(sendgridApiKey);

// Re-export types from core service for backward compatibility
export type { EmailRecipient, EmailData, RFQEmailData };

/**
 * Send a single email
 * @param emailData The email data to send
 * @param providedEmailSettings Optional email settings to use instead of retrieving from database
 * @returns The SendGrid response
 */
export async function sendEmail(
  emailData: EmailData,
  providedEmailSettings?: Awaited<ReturnType<typeof getFormattedEmailSettings>>
): Promise<any> {
  // Validate the email data
  const validationResult = validateEmailData(emailData);

  if (!validationResult.success) {
    logger.error("Email validation failed:", validationResult.error);
    throw new Error(
      validationResult.error?.message || "Email validation failed",
    );
  }

  // Clone the email data to avoid modifying the original
  const emailToSend = { ...validationResult.data! };

  // Use provided email settings or get from database
  // This will throw an error if settings are not configured
  const emailSettings = providedEmailSettings || await getFormattedEmailSettings();

  // Determine if sandbox mode should be enabled based on environment and settings
  // 1. If EMAIL_FORCE_SANDBOX is explicitly set to 'true', force sandbox mode
  // 2. If NODE_ENV is 'development' and EMAIL_FORCE_SANDBOX is not 'false', force sandbox mode
  // 3. Otherwise, use the sandbox mode from database settings
  const forceSandbox = process.env.EMAIL_FORCE_SANDBOX === 'true';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const disableForcedSandbox = process.env.EMAIL_FORCE_SANDBOX === 'false';

  // Determine final sandbox mode status
  const sandboxMode = forceSandbox || (isDevelopment && !disableForcedSandbox) || emailSettings.sandboxMode;

  // If sandbox mode is enabled, check for sandbox recipient email
  if (sandboxMode) {
    // Throw an error if sandbox mode is enabled but no sandbox recipient is configured
    if (!emailSettings.sandboxRecipientEmail) {
      throw new Error("Sandbox mode is enabled but no sandbox recipient email is configured");
    }

    const sandboxRecipient = emailSettings.sandboxRecipientEmail;
    // Store the original recipient in a header for reference
    const originalRecipient = emailToSend.to.email;

    // Redirect the email to the sandbox recipient
    emailToSend.to = {
      email: sandboxRecipient,
      name: `SANDBOX: ${emailToSend.to.name || originalRecipient}`,
    };

    // Modify the subject to indicate it's a sandbox email
    emailToSend.subject = `[SANDBOX - Original recipient: ${originalRecipient}] ${emailToSend.subject}`;

    // Log the reason for sandbox mode
    if (forceSandbox) {
      logger.info(`Sandbox mode forced by EMAIL_FORCE_SANDBOX environment variable`);
    } else if (isDevelopment && !disableForcedSandbox) {
      logger.info(`Sandbox mode forced by NODE_ENV=development environment`);
    } else {
      logger.info(`Sandbox mode enabled by database settings`);
    }

    logger.info(
      `Redirecting email from ${originalRecipient} to ${sandboxRecipient}`,
    );
  }

  // Get the domain for Message-ID generation
  const fromEmail = emailSettings.defaultSenderEmail;

  // Validate sender email
  if (!fromEmail) {
    throw new Error("Sender email is not configured in email settings");
  }

  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fromEmail)) {
    throw new Error(`Invalid sender email format: ${fromEmail}`);
  }

  const domain = extractDomainFromEmail(fromEmail);

  // Validate domain
  if (!domain) {
    throw new Error(`Could not extract domain from sender email: ${fromEmail}`);
  }

  // Generate a unique Message-ID if not provided
  const messageId = emailToSend.messageId || generateMessageId(domain);

  // Prepare the message for SendGrid
  const msg = {
    to: {
      email: emailToSend.to.email,
      name: emailToSend.to.name,
    },
    from: {
      email: fromEmail,
      name: emailSettings.defaultSenderName,
    },
    subject: emailToSend.subject,
    [emailToSend.isHtml ? "html" : "text"]: emailToSend.body,
    trackingSettings: {
      clickTracking: {
        enable: true,
        enableText: true,
      },
      openTracking: {
        enable: true,
      },
    },
    customArgs: {
      tracking_id: emailToSend.trackingId || "",
    },
    attachments: emailToSend.attachments,
    headers: {
      "Message-ID": messageId,
    },
    category: "Logistics RFQ",
  };

  try {
    logger.info(
      `Sending email to ${emailToSend.to.email} with subject "${emailToSend.subject}"`,
    );

    // Create a properly formatted SendGrid email
    // Note: SendGrid API expects 'to' to be an array of recipients
    const sendGridEmail: any = {
      to: [msg.to], // Convert to array as required by SendGrid API
      from: {
        email: fromEmail!,
        name: msg.from.name || '',
      },
      subject: msg.subject,
      headers: msg.headers,
      customArgs: msg.customArgs,
      trackingSettings: msg.trackingSettings,
    };

    // Add content based on whether it's HTML or text
    if (emailToSend.isHtml) {
      // If HTML content is provided, include both HTML and plain text versions
      // This improves compatibility with email clients that don't support HTML
      if (emailToSend.textBody) {
        // If a specific text alternative is provided, use it
        sendGridEmail.content = [
          {
            type: "text/plain",
            value: emailToSend.textBody,
          },
          {
            type: "text/html",
            value: msg.html as string,
          }
        ];
      } else {
        // If no text alternative is provided, just use HTML
        sendGridEmail.content = [
          {
            type: "text/html",
            value: msg.html as string,
          },
        ];
      }
    } else {
      // If it's a text-only email, just use the text content
      sendGridEmail.content = [
        {
          type: "text/plain",
          value: msg.text as string,
        },
      ];
    }

    // Add attachments if present
    if (msg.attachments && msg.attachments.length > 0) {
      sendGridEmail.attachments = msg.attachments;
    }

    // Use reply-to from settings if not specified in the email data
    const replyToEmail = emailToSend.replyTo || emailSettings.replyToEmail;

    // If replyTo is specified, add it as a separate property (not in headers)
    if (replyToEmail) {
      sendGridEmail.replyTo = {
        email: replyToEmail,
        name: msg.from.name,
      };
    }

    // Add BCC recipients if configured in settings
    if (emailSettings.bccRecipients && Array.isArray(emailSettings.bccRecipients) && emailSettings.bccRecipients.length > 0) {
      // Validate and format BCC recipients for SendGrid
      const validBccRecipients = emailSettings.bccRecipients
        .filter(email => email && typeof email === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
        .map(email => ({
          email: email,
        }));

      if (validBccRecipients.length > 0) {
        sendGridEmail.bcc = validBccRecipients;
        logger.info(`Adding ${validBccRecipients.length} BCC recipients to email`);
      } else {
        logger.warn(`No valid BCC recipients found in settings`);
      }
    }

    // Log the SendGrid email object for debugging (without sensitive content)
    const debugSendGridEmail = {
      ...sendGridEmail,
      content: sendGridEmail.content ? sendGridEmail.content.map((c: any) => ({ type: c.type, length: c.value ? c.value.length : 0 })) : [],
    };
    logger.info(`SendGrid email object: ${JSON.stringify(debugSendGridEmail, null, 2)}`);

    // Validate required fields for SendGrid API
    if (!sendGridEmail.from || !sendGridEmail.from.email) {
      throw new Error("Sender email is missing");
    }

    if (!sendGridEmail.to || !Array.isArray(sendGridEmail.to) || sendGridEmail.to.length === 0) {
      throw new Error("Recipients array is missing or empty");
    }

    // Check each recipient in the array
    for (const recipient of sendGridEmail.to) {
      if (!recipient.email) {
        throw new Error("Recipient email is missing in recipients array");
      }
    }

    if (!sendGridEmail.subject) {
      throw new Error("Email subject is missing");
    }

    // Validate that we have content
    if (!sendGridEmail.content || sendGridEmail.content.length === 0) {
      throw new Error("Email content is missing");
    }

    // Validate that content values are not empty
    for (const content of sendGridEmail.content) {
      if (!content.value || content.value.trim() === '') {
        throw new Error(`Email ${content.type} content is empty`);
      }

      // Check for very large content that might exceed SendGrid limits
      if (content.value && content.value.length > 1000000) { // 1MB limit
        logger.warn(`Email ${content.type} content is very large (${content.value.length} bytes)`);
      }
    }

    // Validate that from email is properly formatted
    if (sendGridEmail.from && sendGridEmail.from.email &&
        !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(sendGridEmail.from.email)) {
      throw new Error(`Invalid sender email format: ${sendGridEmail.from.email}`);
    }

    // Validate that each recipient email is properly formatted
    if (sendGridEmail.to && Array.isArray(sendGridEmail.to)) {
      for (const recipient of sendGridEmail.to) {
        if (recipient.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(recipient.email)) {
          throw new Error(`Invalid recipient email format: ${recipient.email}`);
        }
      }
    }

    // Send the email
    const response = await sgMail.send(sendGridEmail);
    logger.info(`Email sent successfully to ${emailToSend.to.email}`);
    return response;
  } catch (error) {
    // Log detailed error information for debugging
    if (error && typeof error === 'object' && 'response' in error) {
      const response = (error as any).response;
      if (response && response.body) {
        // Log the full error details
        logger.error("SendGrid API Error Details:", {
          statusCode: response.statusCode,
          body: response.body,
          headers: response.headers
        });

        // Extract and log the specific error message
        try {
          if (response.body.errors && Array.isArray(response.body.errors)) {
            // Log each error in the errors array
            response.body.errors.forEach((err: any, index: number) => {
              logger.error(`SendGrid Error ${index + 1}:`, JSON.stringify(err, null, 2));
            });
          }
        } catch (parseError) {
          logger.error("Error parsing SendGrid error response:", parseError);
        }
      }
    }

    // Log the full error object for debugging
    logger.error("Full SendGrid Error:", error);

    const errorMessage = formatEmailError(error, "Failed to send email");
    logger.error("Error sending email:", errorMessage);
    throw new Error(errorMessage);
  }
}

/**
 * Send multiple emails at once
 * @param emailsData Array of email data to send
 * @param providedEmailSettings Optional email settings to use instead of retrieving from database
 * @returns Array of SendGrid responses
 */
export async function sendBulkEmails(
  emailsData: EmailData[],
  providedEmailSettings?: Awaited<ReturnType<typeof getFormattedEmailSettings>>
): Promise<any[]> {
  // Get email settings once if not provided to avoid multiple database calls
  const emailSettings = providedEmailSettings || await getFormattedEmailSettings();

  // Process each email individually to apply sandbox mode if needed
  const promises = emailsData.map((emailData) => sendEmail(emailData, emailSettings));

  try {
    logger.info(`Sending ${emailsData.length} emails in bulk`);
    const responses = await Promise.all(promises);
    logger.info(`Successfully sent ${responses.length} emails in bulk`);
    return responses;
  } catch (error) {
    logger.error("Error sending bulk emails:", error);
    throw error;
  }
}

/**
 * Send an RFQ email with proper formatting
 * @param emailData The RFQ email data to send
 * @returns The SendGrid response
 */
export async function sendRFQEmail(emailData: RFQEmailData): Promise<any> {
  // Validate the RFQ email data
  const validationResult = validateRFQEmailData(emailData);

  if (!validationResult.success) {
    logger.error("RFQ email validation failed:", validationResult.error);
    throw new Error(
      validationResult.error?.message || "RFQ email validation failed",
    );
  }

  const { to, subject, body, rfqDetails, trackingId } = validationResult.data!;

  // Get email settings from database
  // This will throw an error if settings are not configured
  const emailSettings = await getFormattedEmailSettings();

  // Extract RFQ ID from the tracking ID (which should be the rfq_outgoing_emails.id)
  // If not available, extract from the RFQ number
  let rfqId = trackingId;
  if (!rfqId && rfqDetails.rfqNumber) {
    // Try to extract the ID from the RFQ number (e.g., "SF-RFQ-12345678-abcd")
    const match = rfqDetails.rfqNumber.match(/SF-RFQ-([a-f0-9-]+)/i);
    if (match && match[1]) {
      rfqId = match[1];
    }
  }

  // Generate a plus-addressed Reply-To email if we have an RFQ ID
  let replyTo: string | undefined;
  if (rfqId) {
    const baseEmail = emailSettings.defaultSenderEmail;
    replyTo = generateRfqPlusAddress(rfqId, baseEmail);
    logger.info(
      `Generated plus-addressed Reply-To for RFQ ${rfqId}: ${replyTo}`,
    );
  }

  // Extract sender information if provided
  const senderName = emailData.senderInfo?.name || emailSettings.defaultSenderName;
  const senderPhone = emailData.senderInfo?.phone || "";

  // Generate HTML and plain text content using the template
  const emailContent = rfqTemplate({
    rfqNumber: rfqDetails.rfqNumber,
    origin: rfqDetails.origin,
    originCountry: rfqDetails.originCountry,
    destination: rfqDetails.destination,
    destinationCountry: rfqDetails.destinationCountry,
    body: body,
    cargoTypes: rfqDetails.cargoTypes,
    companyName: emailSettings.companyName,
    companyLogoUrl: emailSettings.companyLogoUrl,
    emailFooterText: emailSettings.emailFooterText,
    userName: senderName,
    userPhone: senderPhone,
  });

  // Get the domain for Message-ID generation
  const fromEmail = emailSettings.defaultSenderEmail;
  const domain = extractDomainFromEmail(fromEmail);

  // Generate a unique Message-ID with RFQ prefix
  const messageId = generateMessageId(domain, "rfq-");

  // Log the generated Message-ID for tracking purposes
  logger.info(`Generated Message-ID for RFQ email: ${messageId}`);

  // Log BCC recipients if configured
  if (emailSettings.bccRecipients && emailSettings.bccRecipients.length > 0) {
    logger.info(`Adding ${emailSettings.bccRecipients.length} BCC recipients to RFQ email`);
  }

  // Prepare email data with all necessary information
  const emailDataToSend: EmailData = {
    to,
    subject,
    body: emailContent.html,
    textBody: emailContent.text, // Include plain text alternative
    isHtml: true,
    trackingId,
    messageId,
    replyTo,
  };

  // Send the email with HTML content, passing the already retrieved email settings
  // to avoid an additional database call
  return sendEmail(emailDataToSend, emailSettings);
}
